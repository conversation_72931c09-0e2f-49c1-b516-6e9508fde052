import { Award, MapPin, Store } from "lucide-react";
import Image from "next/image";
import PostIcon from "@/public/assets/images/post.png";
import Product from "@/public/assets/images/product1.png";
import { InvoiceResponse } from "@/lib/types/invoice.types";
import SpecialPrice from "@/components/common/SpecialPrice";
const OrderDetailsPage = async ({ params }: { params: Promise<{ slug: string }>; }) => {
  const { slug } = await params
  console.log(slug);
  const response = await fetch(`http://127.0.0.1:8000/api/v1/invoices/${slug}`, {
    method: "GET",
    headers: {
      "Authorization": "matin",
      "Content-Type": "application/json",
      "Accept": "application/json"
    }
  })
  const invoiceResponse: InvoiceResponse = await response.json()
  const invoiceData = invoiceResponse?.data
  console.log(invoiceResponse);
  const invoiceStatus = (status: string) => {
    if (status == "paid") {
      return "پرداخت شده"
    }
    if (status == "pending") {
      return "در انتظار پرداخت"
    }
  }

  return (
    <section className="bg-white p-5 rounded-3xl">
      <div className="flex justify-between px-3 items-center mb-10 max-md:text-sm max-md:px-0">
        <div className="flex items-center gap-2 h-full max-md:gap-1">
          <div className="p-4 max-md:p-2.5 rounded-b-full bg-gradient-to-t from-gray-100 to-transparent">
            <Store />
          </div>
          <h2 className="m-0">
            جزئیات سفارش
          </h2>
          <span className="bg-gray-200 p-2 text-sm rounded-3xl text-black max-md:text-xs">
            #{slug}
          </span>
        </div>
        <div>
          <button className="text-primary border px-4 py-2 rounded-xl max-md:px-2 max-md:py-3">
            مشاهده فاکتور
          </button>
        </div>
      </div>
      <div className="flex flex-col gap-4 md:grid md:grid-flow-col md:grid-rows-3 md:gap-4 md:grid-cols-3">
        <div className="max-md:order-1 text-sm col-span-2 grid grid-cols-4 max-md:grid-cols-2 gap-5 max-md:gap-3 h-fit max-md:mb-5">
          <div className="bg-gray-200 border-gray-400 p-2 rounded-2xl flex flex-col gap-3 ">
            <p>تاریخ سفارش</p>
            <p className="text-black"> {invoiceData.creation_date} </p>
          </div>
          <div className="bg-primary text-white border-primary p-2 rounded-2xl flex flex-col gap-3 ">
            <p>وضعیت پرداخت</p>
            <p> {invoiceStatus(invoiceData.status)} </p>
          </div>
          <div className="bg-gray-200 border-gray-400 p-2 rounded-2xl flex flex-col gap-3 ">
            <p>تعداد سفارش</p>
            <p className="text-black"> 0 کالا</p>
          </div>
          <div className="bg-gray-200 border-gray-400 p-2 rounded-2xl flex flex-col gap-3 ">
            <p>وضعیت</p>
            <p className="text-black"> - </p>
          </div>
        </div>
        <div className="col-span-2 row-span-2 max-md:order-3">
          <div className="group border border-gray-400 group-hover:border-primary p-5 max-md:p-2 rounded-3xl transition-colors duration-300 hover:border-primary">
            <div className="flex gap-3">
              <div>
                <MapPin
                  className="transition-colors duration-300 group-hover:stroke-primary"
                  size={24}
                />
              </div>
              <div className="flex flex-col gap-3 text-sm">
                <p className="md:text-lg transition-colors duration-300 group-hover:text-primary">
                  {invoiceData.address.address}{" "}
                </p>
                <div className="text-xs text-gray-400 flex flex-col gap-2">
                  <p>
                    کد پستی: <span> {invoiceData.address.zip_code} </span>
                  </p>
                  <p>
                    گیرنده: <span> {invoiceData.address.receiver_name} </span>
                  </p>
                </div>
              </div>
            </div>
            <div className="mt-8 bg-gray-200 p-3  px-5 rounded-2xl flex justify-between items-center">
              <div>
                <div className="flex gap-2 mb-3">
                  <Image src={PostIcon} alt="post-icon" />
                  <span className="text-sm">کد پیگیری پست</span>
                </div>
                <div className="text-sm">
                  <p>
                    <span>مرسوله: </span>
                    <span className="max-md:text-xs">11111111111111111111</span>
                  </p>
                </div>
              </div>
              <div>
                <button className="border bg-white rounded-xl px-5 py-3 max-md:px-3 max-md:text-xs whitespace-nowrap">
                  مشاهده در پست
                </button>
              </div>
            </div>
          </div>
        </div>
        <div className="max-md:order-2 row-span-3 max-md:w-full bg-white rounded-3xl p-5 cart-circles-yellow half-circle border border-dashed shadow-md">
          <div>
            <h3> جزئیات مالی سفارش </h3>
          </div>
          <div className="mt-8 flex justify-between">
            <span>روش پرداخت</span>
            <span> - </span>
          </div>
          <div className="mt-8 flex justify-between">
            <span>مبلغ</span>
            <span> {invoiceData.subtotal.toLocaleString()} </span>
          </div>
          <div className="mt-8 flex justify-between">
            <span>سود شما از خرید</span>
            <span className="text-red-500"> {invoiceData.total_discount.toLocaleString()} </span>
          </div>
          <div className="mt-8 flex justify-between">
            <span>هزینه ارسال </span>
            <span> رایگان </span>
          </div>
          <div className="mt-8 flex justify-between">
            <span>جمع کل </span>
            <span> {invoiceData.total.toLocaleString()} </span>
          </div>
        </div>
      </div>
      <div className="mt-10">
        <h2 className="mb-3">لیست محصولا خریداری شده</h2>
        {
          invoiceData.products.map(product => (
            <div key={product.id} className="flex my-5 max-md:flex-col justify-between gap-3 items-center p-5 bg-gray-100 rounded-2xl border">
              <div className="relative w-96 h-36 "> {/* or any fixed height */}
                <Image fill src={product.image || ""} alt="product" className="rounded-xl" />
              </div>
              <div className="w-full flex md:flex-col max-md:flex-wrap gap-2 max-md:mt-3 max-md:text-sm">
                <h3 className="mb-3 w-full max-md:text-base"> {product.name} </h3>
                <p className="flex items-center md:gap-2 gap-1">
                  <Award size={22} />
                  <span>گارانتی 18 ماهه</span>
                </p>
                <p className="flex items-center md:gap-2 max-md:gap-1 max-md:mx-3">
                  <Store />
                  <span> فروشگاه سپر پلاس </span>
                </p>
                <div>
                  {product.sale_price ? (
                    <>
                      <SpecialPrice price={product.price} />
                      <span className='text-red-500 font-bold text-lg max-md:text-sm mr-3'>{product.sale_price.toLocaleString()} تومان</span>
                    </>
                  ) : (
                    <span className="text-red-500 font-bold text-lg max-md:text-sm">{product.price.toLocaleString()} تومان</span>
                  )}
                  {/* <SpecialPrice price={product.price} /> */}
                </div>

              </div>
              <div className="flex md:flex-col gap-3 max-md:px-3">
                <button className="bg-gray-200 text-black rounded-2xl px-12 max-md:px-10 text-sm whitespace-nowrap py-4">سفارش مجدد</button>
                <button className="bg-yellow text-black rounded-2xl px-12 max-md:px-10 text-sm whitespace-nowrap py-4">ثبت دیدگاه</button>
              </div>
            </div>
          ))
        }

      </div>
    </section>
  );
};

export default OrderDetailsPage;
