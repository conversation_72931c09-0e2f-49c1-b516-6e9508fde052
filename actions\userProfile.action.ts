"use server"
import { UserProfileData } from "@/lib/types/types";

export async function updateUserProfile(userData: FormData) {
    try {
        const response = await fetch("http://127.0.0.1:8000/api/v1/profile", {
            method: "POST",
            headers: {
                "Authorization": "matin",
                "Accept": "application/json",
            },
            body: userData
    })
       const data = await response.json();
        return data
    } catch (error) {
        console.error("Error getting addresses:", error);
        return { success: false, error: "Failed to delete address" };
    }
}
export async function getUserProfile() {
    try {
        const response = await fetch("http://127.0.0.1:8000/api/v1/profile", {
            method: "GET",
            headers: {
                "Authorization": "matin",
                "Accept": "application/json",
                "Content-Type": "application/json"
            },
    })
       const data = await response.json();
        return data
    } catch (error) {
        console.error("Error getting addresses:", error);
        return { success: false, error: "Failed to delete address" };
    }
}
