import React, { useCallback, useEffect, useReducer, useRef, useState } from 'react';
import toast from 'react-hot-toast';
import { updateUserAddressAction, createUserAddressAction } from '@/actions/userAddress.action';
import { Button } from '@/components/UI/button';
import { Label } from '@radix-ui/react-dropdown-menu';
import { Input } from '@/components/UI/input';
import NeshanMap, { NeshanMapRef } from '@neshan-maps-platform/react-openlayers';
import '@neshan-maps-platform/react-openlayers/dist/style.css';
import type { UserAddress } from '@/lib/types/types';

// Default center: Tehran
const defaultPosition: [number, number] = [35.6892, 51.3890]; // lat, lng
const MAP_KEY = 'service.59985951cfc749598c567adae5367fe4';

interface AddressFormData {
  id?: string;
  name: string;
  receiver_name: string;
  receiver_phone: string;
  is_recipient_self: boolean;
  province: string;
  city: string;
  zip_code: string;
  address: string;
  latitude: number;
  longitude: number;
}

interface UIState {
  isReverseGeocodingInProgress: boolean;
  isAddressChangeInProgress: boolean;
  position: [number, number];
}

interface AddressModalState {
  formData: AddressFormData;
  uiState: UIState;
}

type Action =
  | { type: 'UPDATE_FIELD'; field: keyof AddressFormData; value: any }
  | { type: 'UPDATE_POSITION'; position: [number, number] }
  | { type: 'SET_REVERSE'; inProgress: boolean }
  | { type: 'SET_FORWARD'; inProgress: boolean }
  | { type: 'UPDATE_ADDRESS'; province: string; city: string; address: string }
  | { type: 'RESET' };

const initialState: AddressModalState = {
  formData: {
    name: '',
    receiver_name: '',
    receiver_phone: '',
    is_recipient_self: true,
    province: '',
    city: '',
    zip_code: '',
    address: '',
    latitude: defaultPosition[0],
    longitude: defaultPosition[1],
  },
  uiState: {
    isReverseGeocodingInProgress: false,
    isAddressChangeInProgress: false,
    position: defaultPosition,
  }
};

function reducer(state: AddressModalState, action: Action): AddressModalState {
  switch (action.type) {
    case 'UPDATE_FIELD':
      return {
        ...state,
        formData: { ...state.formData, [action.field]: action.value }
      };
    case 'UPDATE_POSITION':
      return {
        ...state,
        formData: { ...state.formData, latitude: action.position[0], longitude: action.position[1] },
        uiState: { ...state.uiState, position: action.position }
      };
    case 'SET_REVERSE':
      return { ...state, uiState: { ...state.uiState, isReverseGeocodingInProgress: action.inProgress } };
    case 'SET_FORWARD':
      return { ...state, uiState: { ...state.uiState, isAddressChangeInProgress: action.inProgress } };
    case 'UPDATE_ADDRESS':
      return {
        ...state,
        formData: {
          ...state.formData,
          province: action.province,
          city: action.city,
          address: action.address
        }
      };
    case 'RESET':
      return initialState;
    default:
      return state;
  }
}

const NeshanAddressForm: React.FC<{
  onClose: () => void;
  onAddressCreated?: () => void;
  addressToEdit?: UserAddress | null;
}> = ({ onClose, onAddressCreated, addressToEdit }) => {
  const [state, dispatch] = useReducer(reducer, initialState);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const mapRef = useRef<NeshanMapRef>(null);

  // Prefill when editing
  useEffect(() => {
    if (addressToEdit) {
      dispatch({ type: 'RESET' });
      Object.entries(addressToEdit).forEach(([field, val]) => {
        if (['latitude', 'longitude'].includes(field)) return;
        dispatch({ type: 'UPDATE_FIELD', field: field as keyof AddressFormData, value: val as any });
      });
      dispatch({ type: 'UPDATE_POSITION', position: [addressToEdit.latitude, addressToEdit.longitude] });
    }
  }, [addressToEdit]);

  const { formData, uiState } = state;
  const { position, isReverseGeocodingInProgress } = uiState;
  const { province, city, address, receiver_name } = formData;

  const updateField = useCallback((field: keyof AddressFormData, value: any) => {
    dispatch({ type: 'UPDATE_FIELD', field, value });
  }, []);

  // Forward geocoding
  useEffect(() => {
    async function geocode() {
      if (!province || !city || uiState.isAddressChangeInProgress) return;
      dispatch({ type: 'SET_FORWARD', inProgress: true });
      try {
        const query = `${province} ${city} ${address}`;
        const res = await fetch(`https://api.neshan.org/v5/search?term=${encodeURIComponent(query)}&lat=${defaultPosition[0]}&lng=${defaultPosition[1]}`, {
          headers: { 'Api-Key': MAP_KEY }
        });
        const data = await res.json();
        if (data.items?.length) {
          const loc = data.items[0].location;
          dispatch({ type: 'UPDATE_POSITION', position: [parseFloat(loc.y), parseFloat(loc.x)] });
        }
      } catch (e) {
        console.error(e);
      } finally {
        dispatch({ type: 'SET_FORWARD', inProgress: false });
      }
    }
    geocode();
  }, [province, city, address]);

  // Reverse geocoding
  const handleLocationSelected = useCallback(async (lat: number, lng: number) => {
    dispatch({ type: 'UPDATE_POSITION', position: [lat, lng] });
    dispatch({ type: 'SET_REVERSE', inProgress: true });
    try {
      const res = await fetch(`https://api.neshan.org/v5/reverse?lat=${lat}&lng=${lng}`, {
        headers: { 'Api-Key': MAP_KEY }
      });
      const data = await res.json();
      if (data.status === 'OK' && data.formatted_address) {
        const parts = data.formatted_address.split('،').map((p: string) => p.trim());
        const prov = parts[parts.length - 2] || '';
        const ct = parts[parts.length - 3] || prov;
        const addr = parts.slice(0, parts.length - 2).join('، ') || data.formatted_address;
        dispatch({ type: 'UPDATE_ADDRESS', province: prov, city: ct, address: addr });
      }
    } catch (e) {
      console.error(e);
    } finally {
      dispatch({ type: 'SET_REVERSE', inProgress: false });
    }
  }, []);

  const handleSubmit = async () => {
    setIsSubmitting(true);
    try {
      const payload = { ...formData, zip_code: String(formData.zip_code) };
      const response = addressToEdit
        ? await updateUserAddressAction(payload)
        : await createUserAddressAction(payload);

      if (response.success) {
        toast.success(addressToEdit ? 'آدرس ویرایش شد!' : 'آدرس جدید ثبت شد!');
        onAddressCreated?.();
        onClose();
      } else {
        toast.error(response.message || 'خطا در ثبت آدرس');
      }
    } catch (e) {
      console.error(e);
      toast.error('خطا در ارتباط با سرور');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="flex flex-col h-full p-4" dir="rtl" style={{ textAlign: 'right' }}>
      {/* Map Container */}
      <div className="h-[250px] mb-4 relative">
        <NeshanMap
          ref={mapRef}
          mapKey={MAP_KEY}
          center={{ latitude: position[0], longitude: position[1] }}
          zoom={15}
          defaultType="standard-night"
          traffic={false}
          poi={false}
          style={{ width: '100%', height: '100%' }}
          onInit={(ol, myMap) => {
            myMap.on('click', evt => {
              const [lng, lat] = ol.proj.toLonLat(evt.coordinate);
              handleLocationSelected(lat, lng);
            });
          }}
        />
        {isReverseGeocodingInProgress && (
          <div className="absolute inset-0 bg-black bg-opacity-30 flex items-center justify-center">
            <span className="bg-white p-2 rounded">در حال بارگذاری آدرس...</span>
          </div>
        )}
      </div>

      {/* Form Fields */}
      <div className="grid grid-cols-2 gap-4 mb-4">
        <div>
          <Label>نام آدرس</Label>
          <Input value={formData.name} onChange={e => updateField('name', e.target.value)} />
        </div>
        <div>
          <Label>استان</Label>
          <Input value={province} onChange={e => updateField('province', e.target.value)} />
        </div>
        <div>
          <Label>شهر</Label>
          <Input value={city} onChange={e => updateField('city', e.target.value)} />
        </div>
        <div>
          <Label>کد پستی</Label>
          <Input value={formData.zip_code} onChange={e => updateField('zip_code', e.target.value)} />
        </div>
        <div>
          <Label>نام گیرنده</Label>
          <Input value={receiver_name} onChange={e => updateField('receiver_name', e.target.value)} />
        </div>
        <div>
          <Label>شماره تماس</Label>
          <Input value={formData.receiver_phone} onChange={e => updateField('receiver_phone', e.target.value)} />
        </div>
      </div>
      <div className="mb-4">
        <Label>آدرس کامل</Label>
        <Input value={formData.address} onChange={e => updateField('address', e.target.value)} />
      </div>
      <div className="flex items-center mb-6">
        <input
          type="checkbox"
          checked={formData.is_recipient_self}
          onChange={e => updateField('is_recipient_self', e.target.checked)}
          className="ml-2"
        />
        <span>خودم گیرنده هستم</span>
      </div>

      <Button
        onClick={handleSubmit}
        disabled={
          isSubmitting ||
          !province || !city || !formData.address || !receiver_name || isReverseGeocodingInProgress
        }
        className="w-full"
      >
        {isSubmitting ? <span className="loader animate-spin" /> : (addressToEdit ? 'ذخیره تغییرات' : 'ثبت آدرس')}
      </Button>
    </div>
  );
};

export default NeshanAddressForm;
