import React, { use<PERSON><PERSON>back, useEffect, useReducer, useRef, useState } from 'react';
import toast from 'react-hot-toast';
import { updateUserAddressAction, createUserAddressAction } from '@/actions/userAddress.action';
import { Button } from '@/components/UI/button';
import { Label } from '@radix-ui/react-dropdown-menu';
import { Input } from '@/components/UI/input';
import NeshanMap, { NeshanMapRef } from '@neshan-maps-platform/react-openlayers';
import '@neshan-maps-platform/react-openlayers/dist/style.css';
import type { UserAddress } from '@/lib/types/types';

// Default center: Tehran
const defaultPosition: [number, number] = [35.6892, 51.3890]; // lat, lng
const MAP_KEY = 'web.4ef75c4009054499853b98f436cde308';

// Disable TypeScript strict checks for map integration
/* eslint-disable @typescript-eslint/no-explicit-any */

interface AddressFormData {
  id?: string;
  name: string;
  receiver_name: string;
  receiver_phone: string;
  is_recipient_self: boolean;
  province: string;
  city: string;
  zip_code: string;
  address: string;
  latitude: number;
  longitude: number;
}

interface UIState {
  isReverseGeocodingInProgress: boolean;
  isAddressChangeInProgress: boolean;
  position: [number, number];
}

interface AddressModalState {
  formData: AddressFormData;
  uiState: UIState;
}

type Action =
  | { type: 'UPDATE_FIELD'; field: keyof AddressFormData; value: string | number | boolean }
  | { type: 'UPDATE_POSITION'; position: [number, number] }
  | { type: 'SET_REVERSE'; inProgress: boolean }
  | { type: 'SET_FORWARD'; inProgress: boolean }
  | { type: 'UPDATE_ADDRESS'; province: string; city: string; address: string }
  | { type: 'RESET' };

const initialState: AddressModalState = {
  formData: {
    name: '',
    receiver_name: '',
    receiver_phone: '',
    is_recipient_self: true,
    province: '',
    city: '',
    zip_code: '',
    address: '',
    latitude: defaultPosition[0],
    longitude: defaultPosition[1],
  },
  uiState: {
    isReverseGeocodingInProgress: false,
    isAddressChangeInProgress: false,
    position: defaultPosition,
  }
};

function reducer(state: AddressModalState, action: Action): AddressModalState {
  switch (action.type) {
    case 'UPDATE_FIELD':
      return {
        ...state,
        formData: { ...state.formData, [action.field]: action.value }
      };
    case 'UPDATE_POSITION':
      return {
        ...state,
        formData: { ...state.formData, latitude: action.position[0], longitude: action.position[1] },
        uiState: { ...state.uiState, position: action.position }
      };
    case 'SET_REVERSE':
      return { ...state, uiState: { ...state.uiState, isReverseGeocodingInProgress: action.inProgress } };
    case 'SET_FORWARD':
      return { ...state, uiState: { ...state.uiState, isAddressChangeInProgress: action.inProgress } };
    case 'UPDATE_ADDRESS':
      return {
        ...state,
        formData: {
          ...state.formData,
          province: action.province,
          city: action.city,
          address: action.address
        }
      };
    case 'RESET':
      return initialState;
    default:
      return state;
  }
}

const NeshanAddressForm: React.FC<{
  onClose: () => void;
  onAddressCreated?: () => void;
  addressToEdit?: UserAddress | null;
}> = ({ onClose, onAddressCreated, addressToEdit }) => {
  const [state, dispatch] = useReducer(reducer, initialState);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const mapRef = useRef<NeshanMapRef>(null);
  const mapInstanceRef = useRef<any>(null);
  const markerLayerRef = useRef<any>(null);

  // Prefill when editing
  useEffect(() => {
    if (addressToEdit) {
      dispatch({ type: 'RESET' });
      Object.entries(addressToEdit).forEach(([field, val]) => {
        if (['latitude', 'longitude'].includes(field)) return;
        dispatch({ type: 'UPDATE_FIELD', field: field as keyof AddressFormData, value: val as string | number | boolean });
      });
      dispatch({ type: 'UPDATE_POSITION', position: [addressToEdit.latitude, addressToEdit.longitude] });
    }
  }, [addressToEdit]);

  const { formData, uiState } = state;
  const { position, isReverseGeocodingInProgress } = uiState;
  const { province, city, address, receiver_name } = formData;

  const updateField = useCallback((field: keyof AddressFormData, value: string | number | boolean) => {
    dispatch({ type: 'UPDATE_FIELD', field, value });
  }, []);

  // Add marker to map using OpenLayers (based on Neshan documentation)
  const addMarkerToMap = useCallback((lat: number, lng: number) => {
    if (!mapInstanceRef.current) return;

    try {
      // Access OpenLayers from window (loaded by Neshan)
      const ol = (window as any).ol;
      if (!ol) {
        console.warn('OpenLayers not available');
        return;
      }

      // Remove existing marker layer if it exists
      if (markerLayerRef.current) {
        mapInstanceRef.current.removeLayer(markerLayerRef.current);
      }

      // Create marker feature
      const marker = new ol.Feature({
        geometry: new ol.geom.Point(ol.proj.fromLonLat([lng, lat]))
      });

      // Set marker style (red circle as per your preference)
      marker.setStyle(new ol.style.Style({
        image: new ol.style.Circle({
          radius: 8,
          fill: new ol.style.Fill({ color: '#ef4444' }), // Red color
          stroke: new ol.style.Stroke({ color: '#ffffff', width: 2 }) // White border
        })
      }));

      // Create vector source and layer
      const vectorSource = new ol.source.Vector({
        features: [marker]
      });

      const vectorLayer = new ol.layer.Vector({
        source: vectorSource
      });

      // Add marker layer to map
      mapInstanceRef.current.addLayer(vectorLayer);
      markerLayerRef.current = vectorLayer;

    } catch (error) {
      console.error('Error adding marker to map:', error);
    }
  }, []);

  // Forward geocoding
  useEffect(() => {
    async function geocode() {
      if (!province || !city || uiState.isAddressChangeInProgress) return;
      dispatch({ type: 'SET_FORWARD', inProgress: true });
      try {
        const query = `${province} ${city} ${address}`;
        const res = await fetch(`https://api.neshan.org/v5/search?term=${encodeURIComponent(query)}&lat=${defaultPosition[0]}&lng=${defaultPosition[1]}`, {
          headers: { 'Api-Key': MAP_KEY }
        });
        const data = await res.json();
        if (data.items?.length) {
          const loc = data.items[0].location;
          const newPosition: [number, number] = [parseFloat(loc.y), parseFloat(loc.x)];
          dispatch({ type: 'UPDATE_POSITION', position: newPosition });
        }
      } catch (e) {
        console.error(e);
      } finally {
        dispatch({ type: 'SET_FORWARD', inProgress: false });
      }
    }
    geocode();
  }, [province, city, address, uiState.isAddressChangeInProgress]);

  // Reverse geocoding
  const handleLocationSelected = useCallback(async (lat: number, lng: number) => {
    const newPosition: [number, number] = [lat, lng];
    dispatch({ type: 'UPDATE_POSITION', position: newPosition });
    dispatch({ type: 'SET_REVERSE', inProgress: true });

    // Add marker to map immediately
    addMarkerToMap(lat, lng);

    try {
      const res = await fetch(`https://api.neshan.org/v5/reverse?lat=${lat}&lng=${lng}`, {
        headers: { 'Api-Key': MAP_KEY }
      });
      const data = await res.json();
      if (data.status === 'OK' && data.formatted_address) {
        const parts = data.formatted_address.split('،').map((p: string) => p.trim());
        const prov = parts[parts.length - 2] || '';
        const ct = parts[parts.length - 3] || prov;
        const addr = parts.slice(0, parts.length - 2).join('، ') || data.formatted_address;
        dispatch({ type: 'UPDATE_ADDRESS', province: prov, city: ct, address: addr });
      }
    } catch (e) {
      console.error(e);
    } finally {
      dispatch({ type: 'SET_REVERSE', inProgress: false });
    }
  }, [addMarkerToMap]);

  // Update marker when position changes
  useEffect(() => {
    if (position && mapInstanceRef.current) {
      addMarkerToMap(position[0], position[1]);
    }
  }, [position, addMarkerToMap]);

  const handleSubmit = async () => {
    setIsSubmitting(true);
    try {
      const payload = { ...formData, zip_code: String(formData.zip_code) };
      const response = addressToEdit
        ? await updateUserAddressAction(payload)
        : await createUserAddressAction(payload);

      if (response.success) {
        toast.success(addressToEdit ? 'آدرس ویرایش شد!' : 'آدرس جدید ثبت شد!');
        onAddressCreated?.();
        onClose();
      } else {
        toast.error(response.message || 'خطا در ثبت آدرس');
      }
    } catch (e) {
      console.error(e);
      toast.error('خطا در ارتباط با سرور');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="flex-1 h-[600px] overflow-y-auto [&::-webkit-scrollbar]:w-2
                        [&::-webkit-scrollbar-track]:bg-gray-100
                        [&::-webkit-scrollbar-thumb]:bg-gray-300
                        [&::-webkit-scrollbar-thumb]:rounded-full
                        [&::-webkit-scrollbar]:absolute
                        [&::-webkit-scrollbar]:right-0"
        dir="rtl"
        style={{ direction: 'ltr', textAlign: 'right' }}>
      {/* Form Section */}
      <div className="md:grid md:grid-cols-1 max-md:flex max-md:flex-col h-full">
        <div className="flex flex-col p-4 max-md:px-6">
          {/* Map Container */}
          <div className="w-full h-[230px] max-md:h-[200px] relative rounded-lg overflow-hidden border border-gray-200">
            {/* Map instructions */}
            <div className="absolute top-2 right-2 z-[1000] bg-white p-2 rounded-lg shadow-md text-xs max-w-[200px]">
              <p className="font-bold mb-1">راهنمای نقشه:</p>
              <p>• برای انتخاب مکان، روی نقشه کلیک کنید</p>
              <p>• نشانگر قرمز مکان انتخابی را نشان می‌دهد</p>
            </div>

            <div className="relative w-full h-full">
              <NeshanMap
                ref={mapRef}
                mapKey={MAP_KEY}
                center={{ latitude: position[0], longitude: position[1] }}
                zoom={15}
                defaultType="standard-night"
                traffic={false}
                poi={false}
                style={{ width: '100%', height: '100%' }}
                onInit={(map) => {
                  // Store map instance
                  mapInstanceRef.current = map;

                  // Add initial marker
                  setTimeout(() => {
                    addMarkerToMap(position[0], position[1]);
                  }, 500); // Give map time to fully load

                  // Handle map clicks
                  map.on('click', (evt: any) => {
                    try {
                      // Access OpenLayers from global or map instance
                      const ol = (window as any).ol || (map as any).ol;
                      if (ol && ol.proj && evt.coordinate) {
                        const [lng, lat] = ol.proj.toLonLat(evt.coordinate);
                        handleLocationSelected(lat, lng);
                      }
                    } catch (error) {
                      console.error('Error handling map click:', error);
                    }
                  });
                }}
              />
            </div>

            {isReverseGeocodingInProgress && (
              <div className="absolute inset-0 bg-black bg-opacity-30 flex items-center justify-center">
                <div className="bg-white p-3 rounded-lg">
                  <p>در حال دریافت اطلاعات آدرس...</p>
                </div>
              </div>
            )}
          </div>
        </div>

        <div className="max-md:flex flex-col md:grid grid-cols-2 gap-4 px-6 py-4 bg-white">
          <div>
            <Label className="text-base max-md:text-sm p-1 text-gray-500">نام آدرس</Label>
            <Input
              className="bg-[#F9FAFB] py-7 max-md:py-5 border-[#EFEFEF] rounded-2xl"
              placeholder="مثال: خانه، محل کار"
              value={formData.name}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) => updateField('name', e.target.value)}
            />
          </div>
          <div>
            <Label className="text-base max-md:text-sm p-1 text-gray-500">استان</Label>
            <Input
              className="bg-[#F9FAFB] py-7 max-md:py-5 border-[#EFEFEF] rounded-2xl"
              placeholder="استان خود را انتخاب کنید"
              value={province}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) => updateField('province', e.target.value)}
            />
          </div>
          <div>
            <Label className="text-base max-md:text-sm p-1 text-gray-500">شهر</Label>
            <Input
              className="bg-[#F9FAFB] py-7 max-md:py-5 border-[#EFEFEF] rounded-2xl"
              placeholder="شهر خود را وارد کنید"
              value={city}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) => updateField('city', e.target.value)}
            />
          </div>
          <div>
            <Label className="text-base max-md:text-sm p-1 text-gray-500">کد پستی</Label>
            <Input
              className="bg-[#F9FAFB] py-7 max-md:py-5 border-[#EFEFEF] rounded-2xl"
              placeholder="کد پستی ده رقمی"
              value={formData.zip_code}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) => updateField('zip_code', e.target.value)}
            />
          </div>
          <div>
            <Label className="text-base max-md:text-sm p-1 text-gray-500">نام گیرنده</Label>
            <Input
              className="bg-[#F9FAFB] py-7 max-md:py-5 border-[#EFEFEF] rounded-2xl"
              placeholder="نام گیرنده را وارد کنید"
              value={receiver_name}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) => updateField('receiver_name', e.target.value)}
            />
          </div>
          <div>
            <Label className="text-base max-md:text-sm p-1 text-gray-500">شماره تماس گیرنده</Label>
            <Input
              className="bg-[#F9FAFB] py-7 max-md:py-5 border-[#EFEFEF] rounded-2xl"
              placeholder="09xxxxxxxxx"
              value={formData.receiver_phone}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) => updateField('receiver_phone', e.target.value)}
            />
          </div>

          <div className='col-span-2'>
            <div>
              <Label className="text-base max-md:text-sm p-1 text-gray-500">آدرس کامل</Label>
              <Input
                className="bg-[#F9FAFB] py-7 max-md:py-5 border-[#EFEFEF] rounded-2xl"
                placeholder="مثال: کوچه شهید بهشتی، پلاک 41"
                value={formData.address}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => updateField('address', e.target.value)}
              />
            </div>
            <div className="flex items-center gap-3 mt-5 mr-1">
              <input
                type="checkbox"
                id="is_recipient_self"
                checked={formData.is_recipient_self}
                onChange={(e) => updateField('is_recipient_self', e.target.checked)}
                className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"
              />
              <label htmlFor="is_recipient_self" className="text-sm text-gray-700">
                خودم گیرنده هستم
              </label>
            </div>
          </div>
        </div>

        {/* Submit Button - Positioned at bottom right of inputs */}
        <div className="mt-6 px-6 pb-4 w-1/2">
          <Button
            onClick={handleSubmit}
            disabled={isSubmitting || !province || !city || !formData.address || !receiver_name || isReverseGeocodingInProgress}
            className="bg-primary py-6 text-white hover:bg-blue-400 rounded-xl w-full disabled:bg-gray-400 disabled:cursor-not-allowed flex items-center justify-center gap-2"
          >
            {isSubmitting ? (
              <span className="loader border-t-transparent border-4 border-white rounded-full w-4 h-4 animate-spin"></span>
            ) : null}
            {addressToEdit ? 'ذخیره تغییرات' : 'ثبت آدرس جدید'}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default NeshanAddressForm;