import { updateUserAddressAction, createUserAddressAction } from '@/actions/userAddress.action'
import { Button } from '@/components/UI/button'
import { Label } from '@radix-ui/react-dropdown-menu'
import { Input } from '@/components/UI/input'
import React, { useCallback, useEffect, useReducer, useRef, useState } from 'react'
import toast from 'react-hot-toast'
import { UserAddress } from '@/lib/types/types'



const defaultPosition: [number, number] = [35.6892, 51.3890] // Tehran center

/**
 * Address form data structure matching API requirements
 */
export interface AddressFormData {
    id?: string
    name: string;
    receiver_name: string;
    receiver_phone: string;
    is_recipient_self: boolean;
    province: string;
    city: string;
    zip_code: string;
    address: string;
    latitude: number;
    longitude: number;
}

/**
 * UI state for managing loading states and form interactions
 */
interface UIState {
    isReverseGeocodingInProgress: boolean;
    isAddressChangeInProgress: boolean;
    position: [number, number];
}

/**
 * Combined state for the address modal
 */
interface AddressModalState {
    formData: AddressFormData;
    uiState: UIState;
}

/**
 * Action types for the reducer
 */
type AddressModalAction =
    | { type: 'UPDATE_FIELD'; field: keyof AddressFormData; value: string | number | boolean }
    | { type: 'UPDATE_COORDINATES'; latitude: number; longitude: number }
    | { type: 'UPDATE_POSITION'; position: [number, number] }
    | { type: 'SET_REVERSE_GEOCODING'; isInProgress: boolean }
    | { type: 'SET_ADDRESS_CHANGE'; isInProgress: boolean }
    | { type: 'UPDATE_ADDRESS_FROM_GEOCODING'; province: string; city: string; address: string }
    | { type: 'RESET_FORM' };

/**
 * Initial state for the address modal
 */
const initialState: AddressModalState = {
    formData: {
        name: "",
        receiver_name: "",
        receiver_phone: "",
        is_recipient_self: true,
        province: "",
        city: "",
        zip_code: "",
        address: "",
        latitude: defaultPosition[0],
        longitude: defaultPosition[1],
    },
    uiState: {
        isReverseGeocodingInProgress: false,
        isAddressChangeInProgress: false,
        position: defaultPosition,
    }
};

/**
 * Reducer function to manage address modal state
 */
function addressModalReducer(state: AddressModalState, action: AddressModalAction): AddressModalState {
    switch (action.type) {
        case 'UPDATE_FIELD':
            return {
                ...state,
                formData: {
                    ...state.formData,
                    [action.field]: action.value,
                },
            };

        case 'UPDATE_COORDINATES':
            return {
                ...state,
                formData: {
                    ...state.formData,
                    latitude: action.latitude,
                    longitude: action.longitude,
                },
                uiState: {
                    ...state.uiState,
                    position: [action.latitude, action.longitude],
                },
            };

        case 'UPDATE_POSITION':
            return {
                ...state,
                formData: {
                    ...state.formData,
                    latitude: action.position[0],
                    longitude: action.position[1],
                },
                uiState: {
                    ...state.uiState,
                    position: action.position,
                },
            };

        case 'SET_REVERSE_GEOCODING':
            return {
                ...state,
                uiState: {
                    ...state.uiState,
                    isReverseGeocodingInProgress: action.isInProgress,
                },
            };

        case 'SET_ADDRESS_CHANGE':
            return {
                ...state,
                uiState: {
                    ...state.uiState,
                    isAddressChangeInProgress: action.isInProgress,
                },
            };

        case 'UPDATE_ADDRESS_FROM_GEOCODING':
            return {
                ...state,
                formData: {
                    ...state.formData,
                    province: action.province,
                    city: action.city,
                    address: action.address,
                },
            };

        case 'RESET_FORM':
            return initialState;

        default:
            return state;
    }
}

/**
 * Interactive Neshan Map Component using Neshan Web SDK
 */
const NeshanMap = ({ position, onLocationSelected }: {
    position: [number, number];
    onLocationSelected: (lat: number, lng: number) => void;
}) => {
    const mapRef = useRef<HTMLDivElement>(null);
    const mapInstanceRef = useRef<any>(null);
    const markerRef = useRef<any>(null);
    const [isMapLoaded, setIsMapLoaded] = useState(false);

    // Neshan API key
    const NESHAN_API_KEY = "service.59985951cfc749598c567adae5367fe4";

    // Initialize Neshan map
    useEffect(() => {
        if (!mapRef.current || isMapLoaded) return;

        // Load Neshan map script dynamically
        const loadNeshanMap = async () => {
            try {
                // Check if Neshan is already loaded
                if (typeof window !== 'undefined' && !(window as any).L) {
                    // Load Leaflet CSS
                    const leafletCSS = document.createElement('link');
                    leafletCSS.rel = 'stylesheet';
                    leafletCSS.href = 'https://unpkg.com/leaflet@1.7.1/dist/leaflet.css';
                    document.head.appendChild(leafletCSS);

                    // Load Leaflet JS
                    const leafletScript = document.createElement('script');
                    leafletScript.src = 'https://unpkg.com/leaflet@1.7.1/dist/leaflet.js';
                    document.head.appendChild(leafletScript);

                    await new Promise((resolve) => {
                        leafletScript.onload = resolve;
                    });
                }

                // Initialize the map
                const L = (window as any).L;

                // Create map instance
                const map = L.map(mapRef.current, {
                    center: position,
                    zoom: 15,
                    zoomControl: true,
                    scrollWheelZoom: true,
                    doubleClickZoom: true,
                    dragging: true,
                });

                // Add Neshan tile layer
                L.tileLayer(`https://api.neshan.org/v2/raster/styles/standard-night/tiles/{z}/{x}/{y}.png?key=${NESHAN_API_KEY}`, {
                    attribution: '© Neshan Maps',
                    maxZoom: 18,
                }).addTo(map);

                // Create custom marker icon
                const customIcon = L.divIcon({
                    className: 'custom-marker',
                    html: `
                        <div style="
                            width: 24px;
                            height: 24px;
                            background-color: #ef4444;
                            border: 2px solid white;
                            border-radius: 50%;
                            box-shadow: 0 2px 4px rgba(0,0,0,0.3);
                            display: flex;
                            align-items: center;
                            justify-content: center;
                        ">
                            <div style="
                                width: 8px;
                                height: 8px;
                                background-color: white;
                                border-radius: 50%;
                            "></div>
                        </div>
                    `,
                    iconSize: [24, 24],
                    iconAnchor: [12, 12],
                });

                // Add marker
                const marker = L.marker(position, {
                    icon: customIcon,
                    draggable: true
                }).addTo(map);

                // Handle marker drag
                marker.on('dragend', (e: any) => {
                    const newPos = e.target.getLatLng();
                    onLocationSelected(newPos.lat, newPos.lng);
                });

                // Handle map click
                map.on('click', (e: any) => {
                    const { lat, lng } = e.latlng;
                    marker.setLatLng([lat, lng]);
                    onLocationSelected(lat, lng);
                });

                mapInstanceRef.current = map;
                markerRef.current = marker;
                setIsMapLoaded(true);

            } catch (error) {
                console.error('Error loading Neshan map:', error);
            }
        };

        loadNeshanMap();

        // Cleanup
        return () => {
            if (mapInstanceRef.current) {
                mapInstanceRef.current.remove();
                mapInstanceRef.current = null;
                markerRef.current = null;
                setIsMapLoaded(false);
            }
        };
    }, []);

    // Update marker position when position prop changes
    useEffect(() => {
        if (mapInstanceRef.current && markerRef.current && isMapLoaded) {
            markerRef.current.setLatLng(position);
            mapInstanceRef.current.setView(position, mapInstanceRef.current.getZoom());
        }
    }, [position, isMapLoaded]);

    return (
        <div className="w-full h-full relative">
            <div
                ref={mapRef}
                className="w-full h-full"
                style={{ minHeight: '400px' }}
            />

            {!isMapLoaded && (
                <div className="absolute inset-0 bg-gray-100 flex items-center justify-center">
                    <div className="flex flex-col items-center gap-2">
                        <div className="w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
                        <p className="text-gray-600">در حال بارگذاری نقشه...</p>
                    </div>
                </div>
            )}

            {/* Coordinates display */}
            {isMapLoaded && (
                <div className="absolute bottom-2 left-2 bg-black bg-opacity-70 text-white text-xs px-2 py-1 rounded z-[1000]">
                    {position[0].toFixed(6)}, {position[1].toFixed(6)}
                </div>
            )}
        </div>
    );
};

const NeshanAddressForm = ({ onClose, onAddressCreated, addressToEdit }: {
    onClose: () => void;
    onAddressCreated?: () => void;
    addressToEdit?: UserAddress | null;
}) => {
    const [state, dispatch] = useReducer(addressModalReducer, initialState);
    const [isSubmitting, setIsSubmitting] = useState(false); // Track submission state

    // Pre-fill form when editing
    useEffect(() => {
        if (addressToEdit) {
            dispatch({
                type: 'UPDATE_FIELD',
                field: 'id',
                value: addressToEdit.id || ""
            });
            dispatch({
                type: 'UPDATE_FIELD',
                field: 'name',
                value: addressToEdit.name || ""
            });
            dispatch({
                type: 'UPDATE_FIELD',
                field: 'receiver_name',
                value: addressToEdit.receiver_name || ""
            });
            dispatch({
                type: 'UPDATE_FIELD',
                field: 'receiver_phone',
                value: addressToEdit.receiver_phone || ""
            });
            dispatch({
                type: 'UPDATE_FIELD',
                field: 'province',
                value: addressToEdit.province || ""
            });
            dispatch({
                type: 'UPDATE_FIELD',
                field: 'city',
                value: addressToEdit.city || ""
            });
            dispatch({
                type: 'UPDATE_FIELD',
                field: 'zip_code',
                value: addressToEdit.zip_code || ""
            });
            dispatch({
                type: 'UPDATE_FIELD',
                field: 'address',
                value: addressToEdit.address || ""
            });
            dispatch({
                type: 'UPDATE_COORDINATES',
                latitude: addressToEdit.latitude || defaultPosition[0],
                longitude: addressToEdit.longitude || defaultPosition[1]
            });
        } else {
            dispatch({ type: 'RESET_FORM' });
        }
    }, [addressToEdit]);


    // Destructure state for easier access
    const { formData, uiState } = state;
    const {
        name,
        receiver_name,
        receiver_phone,
        is_recipient_self,
        province,
        city,
        zip_code,
        address
    } = formData;

    const {
        isReverseGeocodingInProgress,
        position
    } = uiState;

    /**
     * Helper function to update form fields
     */
    const updateField = useCallback((field: keyof AddressFormData, value: string | number | boolean) => {
        dispatch({ type: 'UPDATE_FIELD', field, value });
    }, []);

    // No need for Leaflet icon fixes with Neshan map

    // Forward geocoding: Convert address text to coordinates using Neshan API
    const handleAddressChange = useCallback(async () => {
        // Skip if we're currently processing a reverse geocoding request
        if (isReverseGeocodingInProgress || !province || !city) return;

        dispatch({ type: 'SET_ADDRESS_CHANGE', isInProgress: true });
        try {
            const query = `${province} ${city} ${address}`;
            const res = await fetch(`https://api.neshan.org/v5/search?term=${encodeURIComponent(query)}&lat=35.6892&lng=51.3890`, {
                headers: {
                    "Api-Key": "service.59985951cfc749598c567adae5367fe4",
                },
            });
            const data = await res.json();
            if (data.items && data.items.length > 0) {
                const { location } = data.items[0];
                dispatch({
                    type: 'UPDATE_POSITION',
                    position: [parseFloat(location.y), parseFloat(location.x)]
                });
            }
        } catch (error) {
            console.error("Error in forward geocoding:", error);
        } finally {
            dispatch({ type: 'SET_ADDRESS_CHANGE', isInProgress: false });
        }
    }, [province, city, address, isReverseGeocodingInProgress]);

    // Reverse geocoding: Convert coordinates to address using Neshan API
    const handleLocationSelected = useCallback(async (lat: number, lng: number) => {
        dispatch({ type: 'SET_REVERSE_GEOCODING', isInProgress: true });
        try {
            // Use Neshan reverse geocoding API
            const response = await fetch(
                `https://api.neshan.org/v5/reverse?lat=${lat}&lng=${lng}`,
                {
                    headers: {
                        "Api-Key": "service.59985951cfc749598c567adae5367fe4"
                    }
                }
            );

            const data = await response.json();

            if (data && data.status === "OK" && data.formatted_address) {
                // Extract address components from Neshan response
                const formattedAddress = data.formatted_address;

                // Try to parse the formatted address to extract components
                // Neshan typically returns: "address, neighbourhood, city, province, country"
                const addressParts = formattedAddress.split('،').map((part: string) => part.trim());

                let detectedProvince = "";
                let detectedCity = "";
                let detailedAddress = "";

                // Extract province and city from the address parts
                // Usually the last parts contain province and city information
                if (addressParts.length >= 2) {
                    // Try to identify province (usually contains استان)
                    const provinceCandidate = addressParts.find((part: string) =>
                        part.includes('استان') || part.includes('تهران') || part.includes('اصفهان') ||
                        part.includes('شیراز') || part.includes('مشهد') || part.includes('کرج')
                    );

                    if (provinceCandidate) {
                        detectedProvince = provinceCandidate.replace('استان', '').trim();
                    }

                    // City is usually before province
                    const cityIndex = addressParts.findIndex((part: string) => part === provinceCandidate);
                    if (cityIndex > 0) {
                        detectedCity = addressParts[cityIndex - 1];
                    } else if (addressParts.length >= 2) {
                        detectedCity = addressParts[addressParts.length - 2];
                    }

                    // Detailed address is usually the first few parts
                    detailedAddress = addressParts.slice(0, Math.max(1, addressParts.length - 2)).join('، ');
                }

                // Fallback: use the full formatted address if parsing fails
                if (!detectedProvince && !detectedCity) {
                    detailedAddress = formattedAddress;
                    // Try to extract Tehran as a common case
                    if (formattedAddress.includes('تهران')) {
                        detectedProvince = 'تهران';
                        detectedCity = 'تهران';
                    }
                }

                // Update all address fields at once
                dispatch({
                    type: 'UPDATE_ADDRESS_FROM_GEOCODING',
                    province: detectedProvince,
                    city: detectedCity,
                    address: detailedAddress
                });
            }
        } catch (error) {
            console.error("Error in reverse geocoding:", error);
            // Fallback to a simple address format
            dispatch({
                type: 'UPDATE_ADDRESS_FROM_GEOCODING',
                province: 'تهران', // Default fallback
                city: 'تهران',
                address: `عرض جغرافیایی: ${lat.toFixed(6)}, طول جغرافیایی: ${lng.toFixed(6)}`
            });
        } finally {
            dispatch({ type: 'SET_REVERSE_GEOCODING', isInProgress: false });
        }
    }, []);

    // Update map when address fields change
    useEffect(() => {
        if ((province || city || address) && !isReverseGeocodingInProgress) {
            handleAddressChange();
        }
    }, [province, city, address, handleAddressChange, isReverseGeocodingInProgress]);

    const handleCreateOrUpdateAddress = async () => {
        setIsSubmitting(true);
        // Prepare the address data object
        const addressData: AddressFormData = {
            id: addressToEdit?.id, // Include ID only for updates
            name: formData.name,
            receiver_name: formData.receiver_name,
            receiver_phone: formData.receiver_phone,
            is_recipient_self: formData.is_recipient_self,
            province: formData.province,
            city: formData.city,
            zip_code: String(formData.zip_code),
            address: formData.address,
            latitude: formData.latitude,
            longitude: formData.longitude
        };

        try {
            if (addressToEdit) {
                // Update existing address
                const updateResponse = await updateUserAddressAction(addressData);

                if (updateResponse.success) {
                    toast.success("آدرس با موفقیت ویرایش شد");
                    dispatch({ type: 'RESET_FORM' });
                    onAddressCreated?.();
                    onClose();
                } else {
                    toast.error(updateResponse.message || "خطا در ویرایش آدرس");
                }
            } else {
                // Create new address
                const createResponse = await createUserAddressAction(addressData);

                if (createResponse.success) {
                    toast.success("آدرس جدید با موفقیت ایجاد شد");
                    dispatch({ type: 'RESET_FORM' });
                    onAddressCreated?.();
                    onClose();
                } else {
                    toast.error(createResponse.message || "خطا در ایجاد آدرس");
                }
            }
        } catch (error) {
            console.error("Error saving address:", error);
            toast.error("خطا در ارتباط با سرور");
        } finally {
            setIsSubmitting(false);
        }
    };

    return (
        <div className="flex-1 h-[600px] overflow-y-auto [&::-webkit-scrollbar]:w-2
                            [&::-webkit-scrollbar-track]:bg-gray-100
                            [&::-webkit-scrollbar-thumb]:bg-gray-300
                            [&::-webkit-scrollbar-thumb]:rounded-full
                            [&::-webkit-scrollbar]:absolute
                            [&::-webkit-scrollbar]:right-0"
            dir="rtl"
            style={{ direction: 'ltr', textAlign: 'right' }}>
            {/* Form Section */}
            <div className="md:grid md:grid-cols-1 max-md:flex max-md:flex-col h-full ">
                <div className="flex flex-col p-4 max-md:px-6">
                    {/* Map Container */}
                    <div className="w-full h-[230px] max-md:h-[200px] relative rounded-lg overflow-hidden border border-gray-200">
                        {/* Map instructions */}
                        <div className="absolute top-2 right-2 z-[1000] bg-white p-2 rounded-lg shadow-md text-xs max-w-[180px]">
                            <p className="font-bold mb-1">راهنمای نقشه:</p>
                            <p>برای انتخاب مکان، روی نقشه کلیک کنید یا نشانگر را بکشید.</p>
                        </div>

                        <NeshanMap
                            position={position}
                            onLocationSelected={(lat, lng) => {
                                dispatch({ type: 'UPDATE_POSITION', position: [lat, lng] });
                                handleLocationSelected(lat, lng);
                            }}
                        />

                        {isReverseGeocodingInProgress && (
                            <div className="absolute inset-0 bg-black bg-opacity-30 flex items-center justify-center">
                                <div className="bg-white p-3 rounded-lg">
                                    <p>در حال دریافت اطلاعات آدرس...</p>
                                </div>
                            </div>
                        )}
                    </div>
                </div>
                <div className="max-md:flex flex-col md:grid grid-cols-2 gap-4 px-6 py-4 bg-white ">
                    <div>
                        <Label className="text-base max-md:text-sm p-1 text-gray-500">نام آدرس</Label>
                        <Input
                            className="bg-[#F9FAFB] py-7 max-md:py-5 border-[#EFEFEF] rounded-2xl"
                            placeholder="مثال: خانه، محل کار"
                            value={name}
                            onChange={(e: React.ChangeEvent<HTMLInputElement>) => updateField('name', e.target.value)}
                        />
                    </div>
                    <div>
                        <Label className="text-base max-md:text-sm p-1 text-gray-500">استان</Label>
                        <Input
                            className="bg-[#F9FAFB] py-7 max-md:py-5 border-[#EFEFEF] rounded-2xl"
                            placeholder="استان خود را انتخاب کنید"
                            value={province}
                            onChange={(e: React.ChangeEvent<HTMLInputElement>) => updateField('province', e.target.value)}
                        />
                    </div>
                    <div>
                        <Label className="text-base max-md:text-sm p-1 text-gray-500">شهر</Label>
                        <Input
                            className="bg-[#F9FAFB] py-7 max-md:py-5 border-[#EFEFEF] rounded-2xl"
                            placeholder="شهر خود را وارد کنید"
                            value={city}
                            onChange={(e: React.ChangeEvent<HTMLInputElement>) => updateField('city', e.target.value)}
                        />
                    </div>
                    <div>
                        <Label className="text-base max-md:text-sm p-1 text-gray-500">کد پستی</Label>
                        <Input
                            className="bg-[#F9FAFB] py-7 max-md:py-5 border-[#EFEFEF] rounded-2xl"
                            placeholder="کد پستی ده رقمی"
                            value={zip_code}
                            onChange={(e: React.ChangeEvent<HTMLInputElement>) => updateField('zip_code', e.target.value)}
                        />
                    </div>

                    <div>
                        <Label className="text-base max-md:text-sm p-1 text-gray-500">نام گیرنده</Label>
                        <Input
                            className="bg-[#F9FAFB] py-7 max-md:py-5 border-[#EFEFEF] rounded-2xl"
                            placeholder="نام گیرنده را وارد کنید"
                            value={receiver_name}
                            onChange={(e: React.ChangeEvent<HTMLInputElement>) => updateField('receiver_name', e.target.value)}
                        />
                    </div>
                    <div>
                        <Label className="text-base max-md:text-sm p-1 text-gray-500">شماره تماس گیرنده</Label>
                        <Input
                            className="bg-[#F9FAFB] py-7 max-md:py-5 border-[#EFEFEF] rounded-2xl"
                            placeholder="09xxxxxxxxx"
                            value={receiver_phone}
                            onChange={(e: React.ChangeEvent<HTMLInputElement>) => updateField('receiver_phone', e.target.value)}
                        />
                    </div>

                    <div className='col-span-2'>
                        <div>
                            <Label className="text-base max-md:text-sm p-1 text-gray-500">آدرس کامل</Label>
                            <Input
                                className="bg-[#F9FAFB] py-7 max-md:py-5 border-[#EFEFEF] rounded-2xl"
                                placeholder="مثال: کوچه شهید بهشتی، پلاک 41"
                                value={address}
                                onChange={(e: React.ChangeEvent<HTMLInputElement>) => updateField('address', e.target.value)}
                            />
                        </div>
                        <div className="flex items-center gap-3 mt-5 mr-1">
                            <input
                                type="checkbox"
                                id="is_recipient_self"
                                checked={is_recipient_self}
                                onChange={(e) => updateField('is_recipient_self', e.target.checked)}
                                className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"
                            />
                            <label htmlFor="is_recipient_self" className="text-sm text-gray-700">
                                خودم گیرنده هستم
                            </label>
                        </div>
                    </div>


                </div>
                {/* Submit Button - Positioned at bottom right of inputs */}
                <div className="mt-6 px-6 pb-4 w-1/2">
                    <Button
                        onClick={handleCreateOrUpdateAddress}
                        disabled={isSubmitting || !province || !city || !address || !receiver_name || isReverseGeocodingInProgress}
                        className="bg-primary py-6 text-white hover:bg-blue-400 rounded-xl w-full disabled:bg-gray-400 disabled:cursor-not-allowed flex items-center justify-center gap-2"
                    >
                        {isSubmitting ? (
                            <span className="loader border-t-transparent border-4 border-white rounded-full w-4 h-4 animate-spin"></span>
                        ) : null}
                        {addressToEdit ? 'ذخیره تغییرات' : 'ثبت آدرس جدید'}
                    </Button>
                </div>

            </div>
        </div>
    )
}

export default NeshanAddressForm